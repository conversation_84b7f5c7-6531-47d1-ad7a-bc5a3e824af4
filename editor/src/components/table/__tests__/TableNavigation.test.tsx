import { describe, expect, it } from "vitest";
import type { TableCell } from "@/types/table";
import { createLogicalGrid } from "@/utils/tableUtils";

/**
 * Test the navigation logic for tables with merged cells.
 * This tests the helper function that finds navigable cells in different directions.
 */
describe("Table Navigation with Merged Cells", () => {
	// Helper function to simulate the navigation logic from TableEditor
	function findNextNavigableCell(
		cells: TableCell[][],
		rows: number,
		columns: number,
		currentRow: number,
		currentCol: number,
		direction: "up" | "down" | "left" | "right",
	): { row: number; col: number } | null {
		const maxRow = rows - 1;
		const maxCol = columns - 1;

		// Create logical grid to understand cell positions
		const grid = createLogicalGrid(cells, rows, columns);

		// Get all unique cell positions (start positions of actual cells)
		const cellPositions: Array<{ row: number; col: number }> = [];
		for (let rowIndex = 0; rowIndex < cells.length; rowIndex++) {
			const row = cells[rowIndex];
			let logicalCol = 0;
			for (let colIndex = 0; colIndex < row.length; colIndex++) {
				const cell = row[colIndex];
				// Find the logical position where this cell starts
				while (
					logicalCol < columns &&
					grid[rowIndex] &&
					grid[rowIndex][logicalCol] &&
					(grid[rowIndex][logicalCol]?.rowIndex !== rowIndex ||
						grid[rowIndex][logicalCol]?.colIndex !== colIndex)
				) {
					logicalCol++;
				}
				if (logicalCol < columns) {
					cellPositions.push({ row: rowIndex, col: logicalCol });
					logicalCol += cell.colspan || 1;
				}
			}
		}

		// Sort positions for easier navigation
		cellPositions.sort((a, b) => {
			if (a.row !== b.row) return a.row - b.row;
			return a.col - b.col;
		});

		// Find current position in the sorted list
		const currentIndex = cellPositions.findIndex(
			(pos) => pos.row === currentRow && pos.col === currentCol,
		);

		if (currentIndex === -1) {
			// Current position not found, return first available position
			return cellPositions.length > 0 ? cellPositions[0] : null;
		}

		switch (direction) {
			case "up": {
				// Find the cell in the previous row that's closest to current column
				const targetRow = currentRow - 1;
				if (targetRow < 0) return null;

				const candidatesInTargetRow = cellPositions.filter(
					(pos) => pos.row === targetRow,
				);
				if (candidatesInTargetRow.length === 0) {
					// No cells in target row, try recursively
					return findNextNavigableCell(
						cells,
						rows,
						columns,
						targetRow,
						currentCol,
						direction,
					);
				}

				// Find closest cell by column
				let closest = candidatesInTargetRow[0];
				for (const candidate of candidatesInTargetRow) {
					if (
						Math.abs(candidate.col - currentCol) <
						Math.abs(closest.col - currentCol)
					) {
						closest = candidate;
					}
				}
				return closest;
			}

			case "down": {
				// Find the cell in the next row that's closest to current column
				const targetRow = currentRow + 1;
				if (targetRow > maxRow) return null;

				const candidatesInTargetRow = cellPositions.filter(
					(pos) => pos.row === targetRow,
				);
				if (candidatesInTargetRow.length === 0) {
					// No cells in target row, try recursively
					return findNextNavigableCell(
						cells,
						rows,
						columns,
						targetRow,
						currentCol,
						direction,
					);
				}

				// Find closest cell by column
				let closest = candidatesInTargetRow[0];
				for (const candidate of candidatesInTargetRow) {
					if (
						Math.abs(candidate.col - currentCol) <
						Math.abs(closest.col - currentCol)
					) {
						closest = candidate;
					}
				}
				return closest;
			}

			case "left": {
				// Find previous cell in same row, or last cell in previous row
				const candidatesInCurrentRow = cellPositions.filter(
					(pos) => pos.row === currentRow && pos.col < currentCol,
				);

				if (candidatesInCurrentRow.length > 0) {
					// Return rightmost cell in current row that's to the left
					return candidatesInCurrentRow[candidatesInCurrentRow.length - 1];
				}

				// No cells to the left in current row, go to previous row
				const targetRow = currentRow - 1;
				if (targetRow < 0) return null;

				const candidatesInTargetRow = cellPositions.filter(
					(pos) => pos.row === targetRow,
				);
				if (candidatesInTargetRow.length === 0) {
					// No cells in target row, try recursively
					return findNextNavigableCell(
						cells,
						rows,
						columns,
						targetRow,
						maxCol,
						direction,
					);
				}

				// Return rightmost cell in target row
				return candidatesInTargetRow[candidatesInTargetRow.length - 1];
			}

			case "right": {
				// Find next cell in same row, or first cell in next row
				const candidatesInCurrentRow = cellPositions.filter(
					(pos) => pos.row === currentRow && pos.col > currentCol,
				);

				if (candidatesInCurrentRow.length > 0) {
					// Return leftmost cell in current row that's to the right
					return candidatesInCurrentRow[0];
				}

				// No cells to the right in current row, go to next row
				const targetRow = currentRow + 1;
				if (targetRow > maxRow) return null;

				const candidatesInTargetRow = cellPositions.filter(
					(pos) => pos.row === targetRow,
				);
				if (candidatesInTargetRow.length === 0) {
					// No cells in target row, try recursively
					return findNextNavigableCell(
						cells,
						rows,
						columns,
						targetRow,
						0,
						direction,
					);
				}

				// Return leftmost cell in target row
				return candidatesInTargetRow[0];
			}

			default:
				return null;
		}
	}

	// Create a test table with merged cells that would cause navigation issues
	const createTestTableWithMergedCells = (): TableCell[][] => [
		// Row 0: [A1, B1, C1, D1, E1]
		[
			{
				content: "A1",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
				verticalAlign: "top" as const,
			},
			{
				content: "B1",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
				verticalAlign: "top" as const,
			},
			{
				content: "C1",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
				verticalAlign: "top" as const,
			},
			{
				content: "D1",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
				verticalAlign: "top" as const,
			},
			{
				content: "E1",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
				verticalAlign: "top" as const,
			},
		],
		// Row 1: [A2-C2 (colspan=3), D2, E2]
		[
			{
				content: "A2-C2",
				colspan: 3,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
				verticalAlign: "top" as const,
			},
			{
				content: "D2",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
				verticalAlign: "top" as const,
			},
			{
				content: "E2",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
				verticalAlign: "top" as const,
			},
		],
		// Row 2: [A3, B3, C3-D3 (colspan=2), E3-E4 (rowspan=2)]
		[
			{
				content: "A3",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
				verticalAlign: "top" as const,
			},
			{
				content: "B3",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
				verticalAlign: "top" as const,
			},
			{
				content: "C3-D3",
				colspan: 2,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
				verticalAlign: "top" as const,
			},
			{
				content: "E3-E4",
				colspan: 1,
				rowspan: 2,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
				verticalAlign: "top" as const,
			},
		],
		// Row 3: [A4, B4, C4, D4] (E4 is occupied by rowspan)
		[
			{
				content: "A4",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
				verticalAlign: "top" as const,
			},
			{
				content: "B4",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
				verticalAlign: "top" as const,
			},
			{
				content: "C4",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
				verticalAlign: "top" as const,
			},
			{
				content: "D4",
				colspan: 1,
				rowspan: 1,
				backgroundColor: null,
				borderWidths: { top: 1, right: 1, bottom: 1, left: 1 },
				verticalAlign: "top" as const,
			},
		],
	];

	it("should navigate right correctly in a table with merged cells", () => {
		const cells = createTestTableWithMergedCells();

		// Test navigation from A1 (0,0) to the right
		const nextPosition = findNextNavigableCell(cells, 4, 5, 0, 0, "right");
		expect(nextPosition).toEqual({ row: 0, col: 1 }); // Should move to B1

		// Test navigation from B1 to the right
		const nextFromB1 = findNextNavigableCell(cells, 4, 5, 0, 1, "right");
		expect(nextFromB1).toEqual({ row: 0, col: 2 }); // Should move to C1
	});

	it("should navigate down correctly across merged cells", () => {
		const cells = createTestTableWithMergedCells();

		// Test navigation from A1 (0,0) down to A2-C2 merged cell
		const nextPosition = findNextNavigableCell(cells, 4, 5, 0, 0, "down");
		expect(nextPosition).toEqual({ row: 1, col: 0 }); // Should move to A2-C2 merged cell

		// Test navigation from B1 down - should also go to A2-C2 merged cell
		const nextFromB1 = findNextNavigableCell(cells, 4, 5, 0, 1, "down");
		expect(nextFromB1).toEqual({ row: 1, col: 0 }); // Should move to A2-C2 merged cell (closest to B1)
	});

	it("should navigate left correctly around merged cells", () => {
		const cells = createTestTableWithMergedCells();

		// Test navigation from E3-E4 rowspan cell (2,4) to the left
		const nextPosition = findNextNavigableCell(cells, 4, 5, 2, 4, "left");
		expect(nextPosition).toEqual({ row: 2, col: 2 }); // Should move to C3-D3 merged cell

		// Test navigation from D2 (1,3) to the left
		const nextFromD2 = findNextNavigableCell(cells, 4, 5, 1, 3, "left");
		expect(nextFromD2).toEqual({ row: 1, col: 0 }); // Should move to A2-C2 merged cell
	});

	it("should navigate up correctly across merged cells", () => {
		const cells = createTestTableWithMergedCells();

		// Test navigation from A3 (2,0) up to A2-C2 merged cell
		const nextPosition = findNextNavigableCell(cells, 4, 5, 2, 0, "up");
		expect(nextPosition).toEqual({ row: 1, col: 0 }); // Should move to A2-C2 merged cell

		// Test navigation from C3-D3 merged cell (2,2) up
		const nextFromC3D3 = findNextNavigableCell(cells, 4, 5, 2, 2, "up");
		expect(nextFromC3D3).toEqual({ row: 1, col: 3 }); // Should move to D2 (closest to column 2)
	});

	it("should handle boundary cases correctly", () => {
		const cells = createTestTableWithMergedCells();

		// Test navigation from top row up (should return null)
		const upFromTop = findNextNavigableCell(cells, 4, 5, 0, 0, "up");
		expect(upFromTop).toBeNull();

		// Test navigation from bottom row down (should return null)
		const downFromBottom = findNextNavigableCell(cells, 4, 5, 3, 0, "down");
		expect(downFromBottom).toBeNull();

		// Test navigation from leftmost cell left (should return null)
		const leftFromLeftmost = findNextNavigableCell(cells, 4, 5, 0, 0, "left");
		expect(leftFromLeftmost).toBeNull();
	});

	it("should wrap to next/previous row when navigating horizontally", () => {
		const cells = createTestTableWithMergedCells();

		// Test navigation from E1 (0,4) to the right - should wrap to next row
		const rightFromE1 = findNextNavigableCell(cells, 4, 5, 0, 4, "right");
		expect(rightFromE1).toEqual({ row: 1, col: 0 }); // Should move to A2-C2 merged cell

		// Test navigation from A2-C2 merged cell (1,0) to the left - should wrap to previous row
		const leftFromA2C2 = findNextNavigableCell(cells, 4, 5, 1, 0, "left");
		expect(leftFromA2C2).toEqual({ row: 0, col: 4 }); // Should move to E1
	});
});
